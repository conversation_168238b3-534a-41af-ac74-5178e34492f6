-- Test script để kiểm tra chức năng tổ hợp môn cho phương thức <PERSON>ọ<PERSON> bạ

-- 1. Kiểm tra phương thức xét tuyển hiện tại
SELECT id, name, code, requires_subject_combinations 
FROM admission_methods 
WHERE is_active = 1 
ORDER BY priority_order;

-- 2. <PERSON>ểm tra tổ hợp môn có sẵn
SELECT id, code, name, subjects 
FROM subject_combinations 
WHERE is_active = 1 
ORDER BY priority_order, code;

-- 3. Tạo test case: Thêm ngành mới với phương thức Học bạ và tổ hợp môn
-- (Sẽ được thực hiện qua giao diện admin)

-- 4. Kiểm tra dữ liệu sau khi thêm ngành học
-- SELECT 
--     m.id,
--     m.name_major,
--     am.name as admission_method,
--     am.requires_subject_combinations,
--     sc.code as combination_code,
--     sc.name as combination_name
-- FROM majors m
-- LEFT JOIN major_admission_methods mam ON m.id = mam.major_id
-- LEFT JOIN admission_methods am ON mam.admission_method_id = am.id
-- LEFT JOIN major_subject_combinations msc ON m.id = msc.major_id
-- LEFT JOIN subject_combinations sc ON msc.subject_combination_id = sc.id
-- WHERE m.name_major = 'Test Ngành Học Bạ'
-- ORDER BY m.id, am.priority_order, sc.code;

-- 5. Test case: Kiểm tra ngành có cả THPT và Học bạ
-- SELECT DISTINCT
--     m.name_major,
--     COUNT(DISTINCT mam.admission_method_id) as total_methods,
--     COUNT(DISTINCT msc.subject_combination_id) as total_combinations
-- FROM majors m
-- LEFT JOIN major_admission_methods mam ON m.id = mam.major_id
-- LEFT JOIN major_subject_combinations msc ON m.id = msc.major_id
-- WHERE m.status_major = 0
-- GROUP BY m.id, m.name_major
-- HAVING total_methods > 1 AND total_combinations > 0;
