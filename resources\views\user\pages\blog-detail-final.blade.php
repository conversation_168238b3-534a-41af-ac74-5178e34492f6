@extends('user.layout')
@section('title', $dataBlog->name_blog)
@section('content')

<style>
  .blog-detail-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px;
    background: #f8f9fa;
    min-height: 100vh;
  }
  
  .blog-content-grid {
    display: flex;
    gap: 40px;
    margin-bottom: 60px;
  }
  
  .main-content {
    flex: 0 0 70%;
    background: white;
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  }
  
  .sidebar {
    flex: 0 0 30%;
  }
  
  .blog-title {
    font-size: 32px;
    font-weight: 700;
    color: #2c3e50;
    line-height: 1.3;
    margin-bottom: 25px;
  }
  
  .blog-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
  }
  
  .meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6c757d;
    font-size: 14px;
  }
  
  .meta-item i {
    color: #007bff;
    font-size: 16px;
  }
  
  .meta-category {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 8px 16px;
    border-radius: 25px;
    font-weight: 500;
  }
  
  .meta-category i {
    color: white;
  }
  
  .blog-description {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    margin-bottom: 30px;
  }
  
  .blog-description h3 {
    color: #495057;
    font-size: 18px;
    margin-bottom: 15px;
    font-weight: 600;
  }
  
  .blog-description p {
    color: #6c757d;
    line-height: 1.7;
    margin: 0;
    font-size: 15px;
  }
  
  .view-count {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #e3f2fd;
    color: #1976d2;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 500;
    margin-bottom: 30px;
  }
  
  .blog-content {
    color: #495057;
    line-height: 1.8;
    font-size: 16px;
  }
  
  .blog-content img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 20px 0;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  }
  
  .blog-content h1, .blog-content h2, .blog-content h3 {
    color: #2c3e50;
    margin-top: 30px;
    margin-bottom: 15px;
  }
  
  .blog-content p {
    margin-bottom: 16px;
  }
  
  .sidebar-widget {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  }
  
  .widget-title {
    font-size: 20px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 3px solid #007bff;
    position: relative;
  }
  
  .widget-title::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 50px;
    height: 3px;
    background: #28a745;
  }
  
  .category-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  
  .category-item {
    margin-bottom: 10px;
  }
  
  .category-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 15px;
    background: #f8f9fa;
    border-radius: 8px;
    text-decoration: none;
    color: #495057;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
  }
  
  .category-link:hover {
    background: #e3f2fd;
    color: #1976d2;
    border-left-color: #1976d2;
    text-decoration: none;
    transform: translateX(5px);
  }
  
  .category-icon {
    margin-right: 10px;
    color: #007bff;
  }
  
  .popular-post {
    display: flex;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #e9ecef;
  }
  
  .popular-post:last-child {
    border-bottom: none;
  }
  
  .popular-image {
    width: 80px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
  }
  
  .popular-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  .popular-post:hover .popular-image img {
    transform: scale(1.1);
  }
  
  .popular-content h4 {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 8px 0;
    line-height: 1.4;
  }
  
  .popular-content h4 a {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
  }
  
  .popular-content h4 a:hover {
    color: #007bff;
  }
  
  .popular-meta {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 12px;
    color: #6c757d;
  }
  
  .latest-section {
    background: white;
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
  }
  
  .section-title {
    font-size: 28px;
    font-weight: 700;
    color: #2c3e50;
    text-align: center;
    margin-bottom: 40px;
    position: relative;
  }
  
  .section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, #007bff, #28a745);
    border-radius: 2px;
  }
  
  .latest-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 25px;
  }
  
  .latest-card {
    flex: 0 0 calc(33.333% - 17px);
    background: #f8f9fa;
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px solid transparent;
  }
  
  .latest-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #007bff;
  }
  
  .latest-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
  }
  
  .latest-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
  
  .latest-card:hover .latest-image img {
    transform: scale(1.05);
  }
  
  .latest-content {
    padding: 20px;
  }
  
  .latest-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
    line-height: 1.4;
  }
  
  .latest-title a {
    color: inherit;
    text-decoration: none;
    transition: color 0.3s ease;
  }
  
  .latest-title a:hover {
    color: #007bff;
  }
  
  .latest-excerpt {
    color: #6c757d;
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 15px;
  }
  
  .latest-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #6c757d;
  }
  
  .latest-author, .latest-date {
    display: flex;
    align-items: center;
    gap: 5px;
  }
  
  @media (max-width: 992px) {
    .blog-content-grid {
      flex-direction: column;
    }
    
    .main-content, .sidebar {
      flex: none;
    }
    
    .latest-card {
      flex: 0 0 calc(50% - 12px);
    }
  }
  
  @media (max-width: 768px) {
    .blog-detail-wrapper {
      padding: 20px 15px;
    }
    
    .main-content {
      padding: 25px 20px;
    }
    
    .blog-title {
      font-size: 24px;
    }
    
    .blog-meta {
      flex-direction: column;
      gap: 10px;
    }
    
    .latest-card {
      flex: 0 0 100%;
    }
  }
</style>

<div class="blog-detail-wrapper">
  @if($dataBlog)
  <!-- Main Content Grid -->
  <div class="blog-content-grid">
    <!-- Main Content -->
    <div class="main-content">
      <h1 class="blog-title">{{ $dataBlog->name_blog }}</h1>

      <div class="blog-meta">
        <div class="meta-item">
          <i class="fas fa-user"></i>
          <span>{{ $dataBlog->author_name }}</span>
        </div>
        <div class="meta-item">
          <i class="fas fa-calendar-alt"></i>
          <span>{{ date('d/m/Y', strtotime($dataBlog->date_blog)) }}</span>
        </div>
        <div class="meta-item meta-category">
          <i class="fas fa-folder"></i>
          <span>{{ $dataBlog->category_name }}</span>
        </div>
      </div>

      <div class="view-count">
        <i class="fas fa-eye"></i>
        <span>{{ number_format($dataBlog->view_blog) }} lượt xem</span>
      </div>

      @if($dataBlog->description_blog)
      <div class="blog-description">
        <h3><i class="fas fa-quote-left"></i> Tóm tắt nội dung</h3>
        <p>{{ $dataBlog->description_blog }}</p>
      </div>
      @endif

      <div class="blog-content">
        @if($dataBlog->image_blog)
          <img src="{{ $dataBlog->image_blog }}" alt="{{ $dataBlog->name_blog }}">
        @endif
        {!! $dataBlog->content_blog !!}
      </div>
    </div>

    <!-- Sidebar -->
    <div class="sidebar">
      <!-- Categories Widget -->
      <div class="sidebar-widget">
        <h3 class="widget-title">
          <i class="fas fa-list"></i> Danh mục bài viết
        </h3>
        <ul class="category-list">
          @foreach($dataBlogCategory as $category)
          <li class="category-item">
            <a href="{{ route('user.blogList') }}?category={{ $category->id }}" class="category-link">
              <span>
                <i class="fas fa-folder category-icon"></i>
                {{ $category->name_category_blog }}
              </span>
              <i class="fas fa-chevron-right"></i>
            </a>
          </li>
          @endforeach
          <li class="category-item">
            <a href="{{ route('user.blogList') }}" class="category-link" style="background: linear-gradient(135deg, #007bff, #28a745); color: white;">
              <span>
                <i class="fas fa-th-large category-icon" style="color: white;"></i>
                Xem tất cả bài viết
              </span>
              <i class="fas fa-chevron-right"></i>
            </a>
          </li>
        </ul>
      </div>

      <!-- Popular Posts Widget -->
      <div class="sidebar-widget">
        <h3 class="widget-title">
          <i class="fas fa-fire"></i> Bài viết phổ biến
        </h3>
        @foreach($popularBlogs as $blog)
        <div class="popular-post">
          <div class="popular-image">
            <img src="{{ $blog->image_blog }}" alt="{{ $blog->name_blog }}">
          </div>
          <div class="popular-content">
            <h4><a href="{{ route('user.blogDetail', $blog->id) }}">{{ Str::limit($blog->name_blog, 60) }}</a></h4>
            <div class="popular-meta">
              <span><i class="fas fa-eye"></i> {{ number_format($blog->view_blog) }}</span>
              <span><i class="fas fa-calendar"></i> {{ date('d/m/Y', strtotime($blog->date_blog)) }}</span>
            </div>
          </div>
        </div>
        @endforeach
      </div>
    </div>
  </div>
  @else
  <div style="background: #f8d7da; color: #721c24; padding: 30px; border-radius: 8px; text-align: center;">
    <i class="fas fa-exclamation-triangle" style="font-size: 24px; margin-bottom: 10px;"></i><br>
    <strong>Không tìm thấy bài viết!</strong>
  </div>
  @endif

  <!-- Latest Posts Section -->
  <div class="latest-section">
    <h2 class="section-title">
      <i class="fas fa-newspaper"></i> Các bài viết mới nhất
    </h2>
    <div class="latest-grid">
      @foreach($latestBlogs as $blog)
      <div class="latest-card">
        <div class="latest-image">
          <img src="{{ $blog->image_blog }}" alt="{{ $blog->name_blog }}">
        </div>
        <div class="latest-content">
          <h3 class="latest-title">
            <a href="{{ route('user.blogDetail', $blog->id) }}">{{ $blog->name_blog }}</a>
          </h3>
          <p class="latest-excerpt">{{ Str::limit($blog->description_blog, 100) }}</p>
          <div class="latest-meta">
            <div class="latest-author">
              <i class="fas fa-user"></i>
              <span>{{ $blog->author_name }}</span>
            </div>
            <div class="latest-date">
              <i class="fas fa-calendar"></i>
              <span>{{ date('d/m/Y', strtotime($blog->date_blog)) }}</span>
            </div>
          </div>
        </div>
      </div>
      @endforeach
    </div>
  </div>
</div>

@endsection
