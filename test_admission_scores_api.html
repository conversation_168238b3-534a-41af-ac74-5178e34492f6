<!DOCTYPE html>
<html>
<head>
    <title>Test Admission Scores API</title>
</head>
<body>
    <h1>Test Admission Scores API</h1>
    <div id="results"></div>

    <script>
        // Test API cho ngành học có ID = 14 (c<PERSON> dữ liệu điể<PERSON> chuẩn)
        function testAPI() {
            const majorId = 14;
            const url = `/api/major/${majorId}/admission-scores`;
            
            console.log('Testing URL:', url);
            
            fetch(url)
                .then(response => {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('API Response:', data);
                    document.getElementById('results').innerHTML = `
                        <h2>API Response:</h2>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('results').innerHTML = `
                        <h2>Error:</h2>
                        <p style="color: red;">${error.message}</p>
                    `;
                });
        }

        // Chạy test khi trang load
        window.onload = testAPI;
    </script>
</body>
</html>
