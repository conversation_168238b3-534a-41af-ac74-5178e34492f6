<?php $__env->startSection('title', 'Blog Detail Test'); ?>
<?php $__env->startSection('content'); ?>

<style>
  .simple-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: white;
  }
  
  .test-section {
    background: #f8f9fa;
    padding: 20px;
    margin: 20px 0;
    border: 2px solid #007bff;
    border-radius: 8px;
  }
</style>

<div class="simple-container">
  <div class="test-section">
    <h1>🔧 BLOG DETAIL DEBUG PAGE</h1>
    <p><strong>Nếu bạn thấy trang này thì routing và layout đang hoạt động!</strong></p>
  </div>

  <?php if($dataBlog): ?>
  <div class="test-section">
    <h2>✅ Dữ liệu Blog</h2>
    <p><strong>ID:</strong> <?php echo e($dataBlog->id); ?></p>
    <p><strong>Tiêu đề:</strong> <?php echo e($dataBlog->name_blog); ?></p>
    <p><strong>Tác giả:</strong> <?php echo e($dataBlog->author_name); ?></p>
    <p><strong>Ngày:</strong> <?php echo e($dataBlog->date_blog); ?></p>
    <p><strong>Danh mục:</strong> <?php echo e($dataBlog->category_name); ?></p>
    <p><strong>Lượt xem:</strong> <?php echo e($dataBlog->view_blog); ?></p>
    <p><strong>Độ dài nội dung:</strong> <?php echo e(strlen($dataBlog->content_blog)); ?> ký tự</p>
  </div>

  <div class="test-section">
    <h2>📝 Mô tả</h2>
    <p><?php echo e($dataBlog->description_blog); ?></p>
  </div>

  <div class="test-section">
    <h2>📄 Nội dung bài viết</h2>
    <div style="border: 1px solid #ddd; padding: 15px; background: white;">
      <?php echo $dataBlog->content_blog; ?>

    </div>
  </div>
  <?php else: ?>
  <div class="test-section" style="background: #f8d7da; border-color: #dc3545;">
    <h2>❌ Lỗi</h2>
    <p>Không tìm thấy dữ liệu bài viết!</p>
  </div>
  <?php endif; ?>

  <?php if(isset($dataBlogCategory) && count($dataBlogCategory) > 0): ?>
  <div class="test-section">
    <h2>📂 Danh mục (<?php echo e(count($dataBlogCategory)); ?>)</h2>
    <ul>
      <?php $__currentLoopData = $dataBlogCategory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cat): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
      <li><?php echo e($cat->name_category_blog); ?></li>
      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </ul>
  </div>
  <?php endif; ?>

  <?php if(isset($popularBlogs) && count($popularBlogs) > 0): ?>
  <div class="test-section">
    <h2>🔥 Bài viết phổ biến (<?php echo e(count($popularBlogs)); ?>)</h2>
    <ul>
      <?php $__currentLoopData = $popularBlogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
      <li><?php echo e($blog->name_blog); ?> (<?php echo e($blog->view_blog); ?> views)</li>
      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </ul>
  </div>
  <?php endif; ?>

  <?php if(isset($latestBlogs) && count($latestBlogs) > 0): ?>
  <div class="test-section">
    <h2>🆕 Bài viết mới nhất (<?php echo e(count($latestBlogs)); ?>)</h2>
    <ul>
      <?php $__currentLoopData = $latestBlogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
      <li><?php echo e($blog->name_blog); ?> (<?php echo e($blog->date_blog); ?>)</li>
      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </ul>
  </div>
  <?php endif; ?>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('user.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\TuyenSinhProjectV3\resources\views/user/pages/blog-detail-simple.blade.php ENDPATH**/ ?>