@extends('user.layout')
@section('title', 'Blog Detail Test')
@section('content')

<style>
  .simple-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: white;
  }
  
  .test-section {
    background: #f8f9fa;
    padding: 20px;
    margin: 20px 0;
    border: 2px solid #007bff;
    border-radius: 8px;
  }
</style>

<div class="simple-container">
  <div class="test-section">
    <h1>🔧 BLOG DETAIL DEBUG PAGE</h1>
    <p><strong>Nếu bạn thấy trang này thì routing và layout đang hoạt động!</strong></p>
  </div>

  @if($dataBlog)
  <div class="test-section">
    <h2>✅ Dữ liệu Blog</h2>
    <p><strong>ID:</strong> {{ $dataBlog->id }}</p>
    <p><strong>Tiêu đề:</strong> {{ $dataBlog->name_blog }}</p>
    <p><strong>Tác gi<PERSON>:</strong> {{ $dataBlog->author_name }}</p>
    <p><strong>Ngày:</strong> {{ $dataBlog->date_blog }}</p>
    <p><strong>Danh mục:</strong> {{ $dataBlog->category_name }}</p>
    <p><strong>Lượt xem:</strong> {{ $dataBlog->view_blog }}</p>
    <p><strong>Độ dài nội dung:</strong> {{ strlen($dataBlog->content_blog) }} ký tự</p>
  </div>

  <div class="test-section">
    <h2>📝 Mô tả</h2>
    <p>{{ $dataBlog->description_blog }}</p>
  </div>

  <div class="test-section">
    <h2>📄 Nội dung bài viết</h2>
    <div style="border: 1px solid #ddd; padding: 15px; background: white;">
      {!! $dataBlog->content_blog !!}
    </div>
  </div>
  @else
  <div class="test-section" style="background: #f8d7da; border-color: #dc3545;">
    <h2>❌ Lỗi</h2>
    <p>Không tìm thấy dữ liệu bài viết!</p>
  </div>
  @endif

  @if(isset($dataBlogCategory) && count($dataBlogCategory) > 0)
  <div class="test-section">
    <h2>📂 Danh mục ({{ count($dataBlogCategory) }})</h2>
    <ul>
      @foreach($dataBlogCategory as $cat)
      <li>{{ $cat->name_category_blog }}</li>
      @endforeach
    </ul>
  </div>
  @endif

  @if(isset($popularBlogs) && count($popularBlogs) > 0)
  <div class="test-section">
    <h2>🔥 Bài viết phổ biến ({{ count($popularBlogs) }})</h2>
    <ul>
      @foreach($popularBlogs as $blog)
      <li>{{ $blog->name_blog }} ({{ $blog->view_blog }} views)</li>
      @endforeach
    </ul>
  </div>
  @endif

  @if(isset($latestBlogs) && count($latestBlogs) > 0)
  <div class="test-section">
    <h2>🆕 Bài viết mới nhất ({{ count($latestBlogs) }})</h2>
    <ul>
      @foreach($latestBlogs as $blog)
      <li>{{ $blog->name_blog }} ({{ $blog->date_blog }})</li>
      @endforeach
    </ul>
  </div>
  @endif
</div>

@endsection
