<?php $__env->startSection('title', 'Trang chủ'); ?>
<?php $__env->startSection('styles'); ?>
  <link href="https://cdn.jsdelivr.net/npm/summernote/dist/summernote-lite.css" rel="stylesheet">
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
<main class="main-content position-relative max-height-vh-100 h-100 border-radius-lg ">
    <!-- Navbar -->
    <nav class="navbar navbar-main navbar-expand-lg px-0 mx-3 shadow-none border-radius-xl" id="navbarBlur" data-scroll="true">
      <div class="container-fluid py-1 px-3">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb bg-transparent mb-0 pb-0 pt-1 px-0 me-sm-6 me-5">
            <li class="breadcrumb-item text-sm"><a class="opacity-5 text-dark" href="javascript:;">Blogs</a></li>
            <li class="breadcrumb-item text-sm text-dark active" aria-current="page">Quản lý bài viết</li>
          </ol>
        </nav>
        <div class="collapse navbar-collapse mt-sm-0 mt-2 me-md-0 me-sm-4" id="navbar">
          <div class="ms-md-auto pe-md-3 d-flex align-items-center">
          </div>
          <ul class="navbar-nav d-flex align-items-center  justify-content-end">
          </ul>
        </div>
      </div>
    </nav>
    <!-- End Navbar -->
    <div class="container-fluid py-2">
      <div class="row">
        <div class="col-12">
          <div class="card my-4">
            <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2">
              <div class="bg-gradient-dark shadow-dark border-radius-lg pt-4 pb-3">
                <h6 class="text-white text-capitalize ps-3">Danh sách bài viết</h6>
              </div>
            </div>
            <div class="card-body px-0 pb-2">
              <div class="table-responsive p-0">
                <table class="table align-items-center mb-0">
                  <thead>
                    <tr>
                      <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Tên</th>
                      <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Tác giả</th>
                      <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Ảnh</th>
                      <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Lượt xem</th>
                      <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Danh mục</th>
                      <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Ngày đăng</th>
                      <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Hành động</th>
                      <th class="text-secondary opacity-7"></th>
                    </tr>
                  </thead>
                  <tbody>
                    <?php $__currentLoopData = $dataBlog; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                      <td>
                            <h6 class="text-xs text-secondary mb-0"><?php echo e($item->name_blog); ?></h6>
                      </td>
                      <td>
                            <h6 class="text-xs text-secondary mb-0"><?php echo e($item->author_name); ?></h6>
                      </td>
                      <td>
                        <img width="20%" src="<?php echo e($item->image_blog); ?>" alt="">
                      </td>
                      <td class="align-middle text-center text-sm">
                        <h6 class="text-xs text-secondary mb-0"><?php echo e($item->view_blog); ?></h6>
                      </td>
                      <td class="align-middle text-center">
                        <h6 class="text-xs text-secondary mb-0"><?php echo e($item->name_category_blog); ?></h6>
                      </td>
                      <td class="align-middle text-center">
                       <h6 class="text-xs text-secondary mb-0"><?php echo e($item->date_blog); ?></h6>
                      </td>
                      <td class="align-middle text-center">
                        <a href="<?php echo e(route('admin.blog.updateView',['id' => $item->id])); ?>" class="text-white font-weight-bold text-xs btn btn-secondary" data-toggle="tooltip" data-original-title="Edit user">
                          Edit
                        </a>
                        <?php if($item->status_blog==0): ?>
                        <form action="/admin/blog/<?php echo e($item->id); ?>" method="POST">
                          <?php echo method_field('delete'); ?>
                          <?php echo csrf_field(); ?>
                             <button type="submit" class="text-white font-weight-bold text-xs btn btn-danger" data-toggle="tooltip" data-original-title="Edit user">
                          Disabled
                        </button>
                        </form>
                        <?php else: ?>
                        <form action="/admin/blog/restore/<?php echo e($item->id); ?>" method="POST">
                          <?php echo method_field('patch'); ?>
                          <?php echo csrf_field(); ?>
                             <button type="submit" class="text-white font-weight-bold text-xs btn btn-success" data-toggle="tooltip" data-original-title="Edit user">
                          Enabled
                        </button>
                        </form>
                        <?php endif; ?>
                       
                      </td>
                    </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-12">
          <div class="card my-4">
            <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2">
              <div class="bg-gradient-dark shadow-dark border-radius-lg pt-4 pb-3">
                <h6 class="text-white text-capitalize ps-3">Thêm bài viết</h6>
              </div>
            </div>
            <div class="card-body px-0 pb-2">
              <form action="" method="POST" enctype="multipart/form-data" class="container mt-4">
  <?php echo csrf_field(); ?>
  <div class="mb-3">
    <label for="title" class="form-label">Tiêu đề bài viết</label>
    <input type="text" class="form-control border p-2" id="title" name="name_blog" required placeholder="Điền tên bài viết">
  </div>

 <div class="mb-3">
    <label for="image" class="form-label">Tải ảnh bìa chọn file</label>
    <input class="form-control border" type="file" id="image_blog1" name="image_blog1">
    <label for="image" class="form-label  mt-3">Tải ảnh bìa bằng link ảnh</label>
    <input class="form-control border p-2" type="text" id="image_blog2" name="image_blog2">
  </div>
<div class="mb-3">
    <label for="content" class="form-label">Mô tả</label>
    <textarea class="form-control border p-2" id="content" name="description_blog" rows="5" required placeholder="Viết mô tả cho bài viết"></textarea>
  </div>
  <div class="mb-3">
    <label for="content"  class="form-label">Nội dung</label>
    <textarea class="form-control border p-2" id="editor" name="content_blog" rows="5" required placeholder="Viết nội dung cho bài viết"></textarea>
  </div>

 <div class="mb-3">
    <label for="content" class="form-label">Danh mục</label>
    <select class="form-control border p-2" name="category_blog_id" id="">
      <?php $__currentLoopData = $dataCategoryBlog; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <option value="<?php echo e($item->id); ?>"><?php echo e($item->name_category_blog); ?></option>
      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </select>
  </div>
  <button type="submit" class="btn btn-primary">Đăng bài viết</button>
</form>

            </div>
          </div>
        </div>
      </div>
    </div>
  </main>
  <script>
    var win = navigator.platform.indexOf('Win') > -1;
    if (win && document.querySelector('#sidenav-scrollbar')) {
      var options = {
        damping: '0.5'
      }
      Scrollbar.init(document.querySelector('#sidenav-scrollbar'), options);
    }
  </script>
  <?php $__env->stopSection(); ?>
  <?php $__env->startSection('scripts'); ?>
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/summernote/dist/summernote-lite.js"></script>
  <script>
    $(document).ready(function () {
      $('#editor').summernote({
        height: 1000,
        placeholder: 'Nhập nội dung...'
      });
    });
  </script>
  <script>
  $(document).ready(function () {
    $('#image_blog1').on('change', function () {
      if ($(this).val()) {
        $('#image_blog2').val('');
      }
    });

    $('#image_blog2').on('input', function () {
      if ($(this).val().trim() !== '') {
        $('#image_blog1').val('');
      }
    });
  });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\TuyenSinhProjectV3\resources\views/admin/pages/blog/index.blade.php ENDPATH**/ ?>