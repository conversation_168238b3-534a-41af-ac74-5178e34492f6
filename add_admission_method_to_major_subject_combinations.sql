-- Thêm trường admission_method_id vào bảng major_subject_combinations
-- để phân biệt tổ hợp môn theo từng phương thức xét tuyển

-- 1. Thê<PERSON> cột mới
ALTER TABLE `major_subject_combinations` 
ADD COLUMN `admission_method_id` bigint UNSIGNED NULL AFTER `major_id`;

-- 2. Thêm foreign key constraint
ALTER TABLE `major_subject_combinations` 
ADD CONSTRAINT `major_subject_combinations_admission_method_id_foreign` 
FOREIGN KEY (`admission_method_id`) REFERENCES `admission_methods` (`id`) ON DELETE CASCADE;

-- 3. <PERSON><PERSON><PERSON> nhật unique constraint để bao gồm admission_method_id
ALTER TABLE `major_subject_combinations` 
DROP INDEX `major_subject_combination_unique`;

ALTER TABLE `major_subject_combinations` 
ADD UNIQUE KEY `major_subject_combination_method_unique` (`major_id`, `subject_combination_id`, `admission_method_id`);

-- 4. <PERSON><PERSON><PERSON> tra cấu trú<PERSON> bảng mới
DESCRIBE `major_subject_combinations`;

-- 5. <PERSON><PERSON><PERSON> tra dữ liệu hiện tại (admission_method_id sẽ là NULL)
SELECT * FROM `major_subject_combinations` LIMIT 5;
