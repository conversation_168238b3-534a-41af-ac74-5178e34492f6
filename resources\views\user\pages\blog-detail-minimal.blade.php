@extends('user.layout')
@section('title', $dataBlog->name_blog)
@section('content')

<style>
  .blog-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: white;
  }
  
  .blog-main {
    display: block;
    width: 100%;
    margin-bottom: 40px;
  }
  
  .blog-content-area {
    display: flex;
    gap: 30px;
    margin-bottom: 50px;
  }
  
  .content-left {
    flex: 2;
    background: #f8f9fa;
    padding: 30px;
    border-radius: 8px;
  }
  
  .content-right {
    flex: 1;
    background: #f8f9fa;
    padding: 30px;
    border-radius: 8px;
  }
  
  .blog-title {
    font-size: 28px;
    color: #333;
    margin-bottom: 20px;
    font-weight: bold;
  }
  
  .blog-meta {
    margin-bottom: 20px;
    padding: 15px;
    background: white;
    border-radius: 5px;
  }
  
  .blog-description {
    margin-bottom: 20px;
    padding: 15px;
    background: white;
    border-radius: 5px;
  }
  
  .blog-content {
    padding: 20px;
    background: white;
    border-radius: 5px;
    line-height: 1.6;
  }
  
  .sidebar-section {
    margin-bottom: 25px;
    padding: 20px;
    background: white;
    border-radius: 5px;
  }
  
  .sidebar-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
  }
  
  .category-link {
    display: block;
    padding: 8px 12px;
    margin-bottom: 5px;
    background: #e9ecef;
    color: #333;
    text-decoration: none;
    border-radius: 4px;
  }
  
  .category-link:hover {
    background: #007bff;
    color: white;
    text-decoration: none;
  }
  
  .popular-item {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
  }
  
  .popular-item:last-child {
    border-bottom: none;
  }
  
  .popular-image {
    width: 60px;
    height: 45px;
    border-radius: 4px;
    overflow: hidden;
  }
  
  .popular-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .popular-content h5 {
    font-size: 14px;
    margin: 0 0 5px 0;
  }
  
  .popular-content h5 a {
    color: #333;
    text-decoration: none;
  }
  
  .popular-content h5 a:hover {
    color: #007bff;
  }
  
  .popular-meta {
    font-size: 12px;
    color: #666;
  }
  
  .latest-section {
    background: white;
    padding: 40px;
    border-radius: 8px;
  }
  
  .latest-title {
    text-align: center;
    font-size: 24px;
    margin-bottom: 30px;
    color: #333;
  }
  
  .latest-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }
  
  .latest-card {
    flex: 0 0 calc(33.333% - 14px);
    background: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
  }
  
  .latest-image {
    width: 100%;
    height: 150px;
    overflow: hidden;
  }
  
  .latest-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .latest-content {
    padding: 15px;
  }
  
  .latest-content h4 {
    font-size: 16px;
    margin: 0 0 10px 0;
  }
  
  .latest-content h4 a {
    color: #333;
    text-decoration: none;
  }
  
  .latest-content h4 a:hover {
    color: #007bff;
  }
  
  .latest-excerpt {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
  }
  
  .latest-meta {
    font-size: 12px;
    color: #666;
  }
  
  @media (max-width: 768px) {
    .blog-content-area {
      flex-direction: column;
    }
    
    .latest-card {
      flex: 0 0 100%;
    }
  }
</style>

<div class="blog-container">
  <!-- Test visibility -->
  <div style="background: red; color: white; padding: 10px; margin: 10px 0; text-align: center;">
    <strong>🔧 TEST: Blog Detail Page Loading...</strong>
  </div>

  @if($dataBlog)
  <div class="blog-main">
    <!-- Main Content Area -->
    <div class="blog-content-area">
      <!-- Left Content -->
      <div class="content-left">
        <h1 class="blog-title">{{ $dataBlog->name_blog }}</h1>
        
        <div class="blog-meta">
          <strong>📝 Thông tin bài viết:</strong><br>
          👤 Tác giả: {{ $dataBlog->author_name }}<br>
          📅 Ngày đăng: {{ date('d/m/Y', strtotime($dataBlog->date_blog)) }}<br>
          📂 Danh mục: {{ $dataBlog->category_name }}<br>
          👁️ Lượt xem: {{ number_format($dataBlog->view_blog) }}
        </div>
        
        <div class="blog-description">
          <strong>📄 Mô tả:</strong><br>
          {{ $dataBlog->description_blog }}
        </div>
        
        <div class="blog-content">
          <strong>📖 Nội dung bài viết:</strong><br><br>
          @if($dataBlog->image_blog)
            <img src="{{ $dataBlog->image_blog }}" alt="{{ $dataBlog->name_blog }}" style="width: 100%; max-width: 500px; margin: 20px 0; border-radius: 8px;">
          @endif
          {!! $dataBlog->content_blog !!}
        </div>
      </div>
      
      <!-- Right Sidebar -->
      <div class="content-right">
        <!-- Categories -->
        <div class="sidebar-section">
          <h3 class="sidebar-title">📂 Danh mục bài viết</h3>
          @foreach($dataBlogCategory as $category)
          <a href="{{ route('user.blogList') }}?category={{ $category->id }}" class="category-link">
            {{ $category->name_category_blog }}
          </a>
          @endforeach
          <a href="{{ route('user.blogList') }}" class="category-link" style="background: #007bff; color: white;">
            📋 Xem tất cả bài viết
          </a>
        </div>
        
        <!-- Popular Posts -->
        <div class="sidebar-section">
          <h3 class="sidebar-title">🔥 Bài viết phổ biến</h3>
          @foreach($popularBlogs as $blog)
          <div class="popular-item">
            <div class="popular-image">
              <img src="{{ $blog->image_blog }}" alt="{{ $blog->name_blog }}">
            </div>
            <div class="popular-content">
              <h5><a href="{{ route('user.blogDetail', $blog->id) }}">{{ Str::limit($blog->name_blog, 50) }}</a></h5>
              <div class="popular-meta">
                👁️ {{ number_format($blog->view_blog) }} | 📅 {{ date('d/m/Y', strtotime($blog->date_blog)) }}
              </div>
            </div>
          </div>
          @endforeach
        </div>
      </div>
    </div>
  </div>
  @else
  <div style="background: #f8d7da; color: #721c24; padding: 20px; border-radius: 5px;">
    <strong>❌ Lỗi:</strong> Không tìm thấy dữ liệu bài viết!
  </div>
  @endif
  
  <!-- Latest Posts Section -->
  <div class="latest-section">
    <h2 class="latest-title">📰 Các bài viết mới nhất</h2>
    <div class="latest-grid">
      @foreach($latestBlogs as $blog)
      <div class="latest-card">
        <div class="latest-image">
          <img src="{{ $blog->image_blog }}" alt="{{ $blog->name_blog }}">
        </div>
        <div class="latest-content">
          <h4><a href="{{ route('user.blogDetail', $blog->id) }}">{{ $blog->name_blog }}</a></h4>
          <p class="latest-excerpt">{{ Str::limit($blog->description_blog, 80) }}</p>
          <div class="latest-meta">
            👤 {{ $blog->author_name }} | 📅 {{ date('d/m/Y', strtotime($blog->date_blog)) }}
          </div>
        </div>
      </div>
      @endforeach
    </div>
  </div>
</div>

@endsection
