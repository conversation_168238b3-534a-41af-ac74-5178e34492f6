@extends('user.layout')
@section('title', $dataBlog->name_blog)
@section('content')



<style>
  .blog-wrapper {
    max-width: 1400px;
    margin: 0 auto;
    padding: 40px 20px;
    background: #f8f9fa;
    min-height: 100vh;
  }

  .blog-main {
    display: flex;
    gap: 40px;
    margin-bottom: 60px;
    width: 100%;
  }

  .blog-content {
    flex: 0 0 68%;
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
  }

  .blog-sidebar {
    flex: 0 0 32%;
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    height: fit-content;
    position: sticky;
    top: 20px;
  }

  .blog-title {
    font-size: 32px;
    color: #2d3748;
    margin-bottom: 20px;
    font-weight: 600;
    line-height: 1.3;
    padding-bottom: 15px;
    border-bottom: 2px solid #e2e8f0;
  }

  .blog-meta {
    background: #f7fafc;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    border: 1px solid #e2e8f0;
  }

  .meta-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 14px;
    color: #4a5568;
  }

  .meta-item:last-child {
    margin-bottom: 0;
  }

  .meta-item strong {
    color: #2d3748;
    margin-right: 8px;
    font-weight: 500;
    min-width: 80px;
  }

  .blog-description {
    background: #f0f8ff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    border-left: 4px solid #3182ce;
  }

  .blog-description h3 {
    color: #2b6cb0;
    font-size: 16px;
    margin-bottom: 12px;
    font-weight: 600;
  }

  .blog-description p {
    color: #4a5568;
    line-height: 1.6;
    margin: 0;
    font-size: 15px;
    font-style: italic;
  }

  .view-badge {
    display: inline-flex;
    align-items: center;
    background: #edf2f7;
    color: #4a5568;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 500;
    margin-bottom: 25px;
    font-size: 14px;
    border: 1px solid #e2e8f0;
  }

  .view-badge::before {
    content: "👁️";
    margin-right: 6px;
    font-size: 14px;
  }
  
  .blog-content-text {
    background: #fafafa;
    padding: 30px;
    border-radius: 8px;
    line-height: 1.8;
    color: #2d3748;
    font-size: 16px;
    border: 1px solid #e2e8f0;
  }

  .blog-content-text img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 25px 0;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  }

  .blog-content-text h1,
  .blog-content-text h2,
  .blog-content-text h3,
  .blog-content-text h4 {
    color: #2d3748;
    margin-top: 30px;
    margin-bottom: 15px;
    font-weight: 600;
  }

  .blog-content-text p {
    margin-bottom: 18px;
    text-align: justify;
  }

  .blog-content-text ul,
  .blog-content-text ol {
    margin: 20px 0;
    padding-left: 25px;
  }

  .blog-content-text li {
    margin-bottom: 8px;
    line-height: 1.6;
  }

  .content-divider {
    height: 1px;
    background: linear-gradient(to right, transparent, #e2e8f0, transparent);
    margin: 30px 0;
  }
  
  .sidebar-box {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 25px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
  }

  .sidebar-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  }

  .sidebar-title {
    font-size: 20px;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 3px solid #667eea;
    position: relative;
    display: flex;
    align-items: center;
  }

  .sidebar-title::before {
    content: "";
    width: 6px;
    height: 6px;
    background: #667eea;
    border-radius: 50%;
    margin-right: 10px;
  }
  
  .category-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 18px;
    margin-bottom: 10px;
    background: white;
    color: #4a5568;
    text-decoration: none;
    border-radius: 10px;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
    font-weight: 500;
    position: relative;
    overflow: hidden;
  }

  .category-item::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
    z-index: 1;
  }

  .category-item:hover::before {
    width: 100%;
  }

  .category-item:hover {
    color: white;
    text-decoration: none;
    transform: translateX(8px);
    border-color: #667eea;
  }

  .category-item span,
  .category-item i {
    position: relative;
    z-index: 2;
  }

  .category-item i {
    font-size: 14px;
    opacity: 0.7;
    transition: all 0.3s ease;
  }

  .category-item:hover i {
    opacity: 1;
    transform: translateX(3px);
  }
  
  .popular-item {
    display: flex;
    gap: 12px;
    padding: 15px 0;
    border-bottom: 1px solid #e9ecef;
  }
  
  .popular-item:last-child {
    border-bottom: none;
  }
  
  .popular-image {
    width: 70px;
    height: 50px;
    border-radius: 6px;
    overflow: hidden;
  }
  
  .popular-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .popular-content h5 {
    font-size: 14px;
    margin: 0 0 8px 0;
    line-height: 1.4;
  }
  
  .popular-content h5 a {
    color: #2c3e50;
    text-decoration: none;
  }
  
  .popular-content h5 a:hover {
    color: #007bff;
  }
  
  .popular-meta {
    font-size: 12px;
    color: #6c757d;
  }
  
  .latest-section {
    background: #f8f9fa;
    padding: 40px;
    border-radius: 10px;
  }
  
  .latest-title {
    text-align: center;
    font-size: 24px;
    color: #2c3e50;
    margin-bottom: 30px;
    font-weight: bold;
  }
  
  .latest-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }
  
  .latest-card {
    flex: 0 0 calc(33.333% - 14px);
    background: white;
    border-radius: 10px;
    overflow: hidden;
    transition: transform 0.3s ease;
  }
  
  .latest-card:hover {
    transform: translateY(-5px);
  }
  
  .latest-image {
    width: 100%;
    height: 180px;
    overflow: hidden;
  }
  
  .latest-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .latest-content {
    padding: 20px;
  }
  
  .latest-content h4 {
    font-size: 16px;
    margin: 0 0 10px 0;
    color: #2c3e50;
  }
  
  .latest-content h4 a {
    color: inherit;
    text-decoration: none;
  }
  
  .latest-content h4 a:hover {
    color: #007bff;
  }
  
  .latest-excerpt {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 15px;
    line-height: 1.5;
  }
  
  .latest-meta {
    font-size: 12px;
    color: #6c757d;
  }
  
  @media (max-width: 768px) {
    .blog-main {
      flex-direction: column;
    }
    
    .blog-wrapper {
      padding: 20px 15px;
    }
    
    .blog-content, .blog-sidebar {
      padding: 20px;
    }
    
    .blog-title {
      font-size: 22px;
    }
    
    .latest-card {
      flex: 0 0 100%;
    }
  }
</style>

<div class="blog-wrapper">
  @if($dataBlog)
  <!-- Main Content -->
  <div class="blog-main">
    <!-- Left Content -->
    <div class="blog-content">
      <h1 class="blog-title">{{ $dataBlog->name_blog }}</h1>

      <div class="blog-meta">
        <div class="meta-item">
          <strong>Tác giả:</strong> {{ $dataBlog->author_name }}
        </div>
        <div class="meta-item">
          <strong>Ngày đăng:</strong> {{ date('d/m/Y', strtotime($dataBlog->date_blog)) }}
        </div>
        <div class="meta-item">
          <strong>Danh mục:</strong> {{ $dataBlog->category_name }}
        </div>
      </div>

      <div class="view-badge">
        {{ number_format($dataBlog->view_blog) }} lượt xem
      </div>

      <div class="content-divider"></div>

      @if($dataBlog->description_blog)
      <div class="blog-description">
        <h3>📋 Tóm tắt nội dung</h3>
        <p>{{ $dataBlog->description_blog }}</p>
      </div>
      @endif

      <div class="blog-content-text">
        @if($dataBlog->image_blog)
          <img src="{{ $dataBlog->image_blog }}" alt="{{ $dataBlog->name_blog }}">
        @endif
        {!! $dataBlog->content_blog !!}
      </div>
    </div>
    
    <!-- Right Sidebar -->
    <div class="blog-sidebar">
      <!-- Categories -->
      <div class="sidebar-box">
        <h3 class="sidebar-title">📂 Danh mục bài viết</h3>
        @foreach($dataBlogCategory as $category)
        <a href="{{ route('user.blogList') }}?category={{ $category->id }}" class="category-item">
          <span>{{ $category->name_category_blog }}</span>
          <i>→</i>
        </a>
        @endforeach
        <a href="{{ route('user.blogList') }}" class="category-item" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-color: #667eea;">
          <span>📋 Xem tất cả bài viết</span>
          <i>→</i>
        </a>
      </div>
      
      <!-- Popular Posts -->
      <div class="sidebar-box">
        <h3 class="sidebar-title">🔥 Bài viết phổ biến</h3>
        @foreach($popularBlogs as $blog)
        <div class="popular-item">
          <div class="popular-image">
            <img src="{{ $blog->image_blog }}" alt="{{ $blog->name_blog }}">
          </div>
          <div class="popular-content">
            <h5><a href="{{ route('user.blogDetail', $blog->id) }}">{{ Str::limit($blog->name_blog, 50) }}</a></h5>
            <div class="popular-meta">
              👁️ {{ number_format($blog->view_blog) }} | 📅 {{ date('d/m/Y', strtotime($blog->date_blog)) }}
            </div>
          </div>
        </div>
        @endforeach
      </div>
    </div>
  </div>
  @else
  <div style="background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; text-align: center;">
    <strong>❌ Không tìm thấy bài viết!</strong>
  </div>
  @endif
  
  <!-- Latest Posts -->
  <div class="latest-section">
    <h2 class="latest-title">📰 Các bài viết mới nhất</h2>
    <div class="latest-grid">
      @foreach($latestBlogs as $blog)
      <div class="latest-card">
        <div class="latest-image">
          <img src="{{ $blog->image_blog }}" alt="{{ $blog->name_blog }}">
        </div>
        <div class="latest-content">
          <h4><a href="{{ route('user.blogDetail', $blog->id) }}">{{ $blog->name_blog }}</a></h4>
          <p class="latest-excerpt">{{ Str::limit($blog->description_blog, 80) }}</p>
          <div class="latest-meta">
            👤 {{ $blog->author_name }} | 📅 {{ date('d/m/Y', strtotime($blog->date_blog)) }}
          </div>
        </div>
      </div>
      @endforeach
    </div>
  </div>
</div>

@endsection
