<?php

namespace App\Http\Controllers\admin;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BlogAdminController extends Controller
{
    public function index()
    {
        $dataBlog = DB::table('blogs')
            ->join('users', 'blogs.author_id', '=', 'users.id')
            ->join('category_blog', 'blogs.category_blog_id', '=', 'category_blog.id')
            ->select(
                'blogs.id',
                'blogs.image_blog',
                'blogs.status_blog',
                'blogs.name_blog',
                'blogs.description_blog',
                'blogs.date_blog',
                'blogs.view_blog',
                'users.name as author_name',
                'category_blog.name_category_blog'
            )
            ->orderByDesc('category_blog.name_category_blog')
            ->get();
        $dataCategoryBlog = DB::table('category_blog')
            ->select('*')
            ->get();
        return view('admin.pages.blog.index', compact('dataBlog', 'dataCategoryBlog'));
    }

    public function createBlog(Request $request)
    {
        if ($request->image_blog1 != '') {
            $request->validate([
                'image_blog1' => 'image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            ]);

            $path = '';

            if ($request->hasFile('image_blog1')) {
                $file = $request->file('image_blog1');

                $filename = time() . '_' . $file->getClientOriginalName();

                $file->move(public_path('imagesSource'), $filename);

                $path = '/imagesSource/' . $filename;
            }
        } else if ($request->image_blog2 != '') {
            $path=$request->image_blog2;
        }


        DB::table('blogs')->insert([
            'name_blog' => $request->name_blog,
            'image_blog' => $path,
            'description_blog' => $request->description_blog,
            'content_blog' => $request->content_blog,
            'author_id' => Auth::user()->id,
            'category_blog_id' => $request->category_blog_id,
        ]);

        return back();
    }
    public function updateView($id)
    {
        $dataBlog = DB::table('blogs')
            ->select('*')
            ->where('id', $id)
            ->first();
        $dataCategoryBlog = DB::table('category_blog')
            ->select('*')
            ->get();

        return view('admin.pages.blog.update', compact('dataBlog', 'dataCategoryBlog'));
    }
    public function updateBlog(Request $request, $id)
    {
         if ($request->image_blog1 != '') {
            $request->validate([
                'image_blog1' => 'image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            ]);

            $path = '';

            if ($request->hasFile('image_blog1')) {
                $file = $request->file('image_blog1');

                $filename = time() . '_' . $file->getClientOriginalName();

                $file->move(public_path('imagesSource'), $filename);

                $path = '/imagesSource/' . $filename;
            }
        } else if ($request->image_blog2 != '') {
            $path=$request->image_blog2;
        }else if($request->image_blog1 == '' && $request->image_blog1 == ''){
            $dataBlog = DB::table('blogs')
                        ->select('image_blog')
                        ->where('id', $id)
                        ->first();
            $path=$dataBlog->image_blog;
        }
        DB::table('blogs')
            ->where('id', $id)
            ->update([
                'name_blog' => $request->name_blog,
                'image_blog' => $path,
                'description_blog' => $request->description_blog,
                'content_blog' => $request->content_blog,
                'category_blog_id' => $request->category_blog_id,
            ]);

        return redirect()->route('admin.blog');
    }
    public function softDeleteBlog($id)
    {
        DB::table('blogs')
            ->where('id', $id)
            ->update(['status_blog' => 1]);

        return back();
    }
    public function restoreBlog($id)
    {
        DB::table('blogs')
            ->where('id', $id)
            ->update(['status_blog' => 0]);

        return back();
    }
    public function getBlogByIdCategory(Request $request, $id)
    {
        $perPage = 9; // Giới hạn 9 bài viết mỗi trang

        if($id == 0){
            $dataBlog = DB::table('blogs')
                ->join('users', 'blogs.author_id', '=', 'users.id')
                ->join('category_blog', 'blogs.category_blog_id', '=', 'category_blog.id')
                ->where('blogs.status_blog', 0)
                ->select(
                    'blogs.id',
                    'blogs.image_blog',
                    'blogs.name_blog',
                    'blogs.description_blog',
                    'blogs.date_blog',
                    'blogs.view_blog',
                    'users.name as author_name',
                    'category_blog.name_category_blog'
                )
                ->orderByDesc('blogs.date_blog')
                ->limit($perPage)
                ->get();
        }else{
            $dataBlog = DB::table('blogs')
                ->join('users', 'blogs.author_id', '=', 'users.id')
                ->join('category_blog', 'blogs.category_blog_id', '=', 'category_blog.id')
                ->where('blogs.category_blog_id', $id)
                ->where('blogs.status_blog', 0)
                ->select(
                    'blogs.id',
                    'blogs.image_blog',
                    'blogs.name_blog',
                    'blogs.description_blog',
                    'blogs.date_blog',
                    'blogs.view_blog',
                    'users.name as author_name',
                    'category_blog.name_category_blog'
                )
                ->orderByDesc('blogs.date_blog')
                ->limit($perPage)
                ->get();
        }

        return response()->json([
                'dataBlog' => $dataBlog
            ]);
    }
}
