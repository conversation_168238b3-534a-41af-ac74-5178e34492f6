-- Test script để kiểm tra tổ hợp môn
-- <PERSON>ể<PERSON> tra dữ liệu hiện tại

-- 1. <PERSON><PERSON><PERSON> tra các tổ hợp môn có sẵn
SELECT * FROM subject_combinations WHERE is_active = 1;

-- 2. <PERSON>ể<PERSON> tra phương thức xét tuyển
SELECT * FROM admission_methods WHERE is_active = 1;

-- 3. <PERSON><PERSON><PERSON> tra ngành học hiện tại
SELECT id, name_major FROM majors WHERE status_major = 0 LIMIT 5;

-- 4. <PERSON>ể<PERSON> tra tổ hợp môn đã được gán cho ngành học
SELECT 
    m.name_major,
    sc.code,
    sc.name as combination_name,
    msc.min_score
FROM major_subject_combinations msc
JOIN majors m ON msc.major_id = m.id
JOIN subject_combinations sc ON msc.subject_combination_id = sc.id
ORDER BY m.name_major, sc.code;

-- 5. <PERSON><PERSON><PERSON> tra phương thức xét tuyển đã được gán cho ngành học
SELECT 
    m.name_major,
    am.name as method_name,
    am.requires_subject_combinations
FROM major_admission_methods mam
JOIN majors m ON mam.major_id = m.id
JOIN admission_methods am ON mam.admission_method_id = am.id
ORDER BY m.name_major, am.priority_order;

-- Test case: Thêm tổ hợp môn cho ngành Kinh tế (id=7)
-- DELETE FROM major_subject_combinations WHERE major_id = 7;
-- INSERT INTO major_subject_combinations (major_id, subject_combination_id, created_at, updated_at) VALUES
-- (7, 1, NOW(), NOW()),  -- A00
-- (7, 3, NOW(), NOW());  -- D01

-- Kiểm tra kết quả
-- SELECT 
--     m.name_major,
--     sc.code,
--     sc.name as combination_name
-- FROM major_subject_combinations msc
-- JOIN majors m ON msc.major_id = m.id
-- JOIN subject_combinations sc ON msc.subject_combination_id = sc.id
-- WHERE m.id = 7;
