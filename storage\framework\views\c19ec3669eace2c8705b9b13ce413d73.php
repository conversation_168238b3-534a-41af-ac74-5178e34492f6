
<?php $__env->startSection('title', $dataBlog->name_blog); ?>

<?php $__env->startPush('styles'); ?>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>



<style>
  .blog-wrapper {
    max-width: 1400px;
    margin: 0 auto;
    padding: 40px 20px;
    background: #f8f9fa;
    min-height: 100vh;
  }

  .blog-main {
    display: flex;
    gap: 40px;
    margin-bottom: 60px;
    width: 100%;
  }

  .blog-content {
    flex: 0 0 68%;
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
  }

  .blog-sidebar {
    flex: 0 0 32%;
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    height: fit-content;
    position: sticky;
    top: 20px;
  }

  .blog-title {
    font-size: 28px;
    color: #2c3e50;
    margin-bottom: 30px;
    font-weight: 600;
    line-height: 1.4;
    text-align: center;
    padding: 0 20px;
  }

  .blog-image-container {
    margin-bottom: 20px;
    text-align: center;
  }

  .blog-main-image {
    width: 100%;
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  }

  .blog-meta {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 20px;
    margin-bottom: 25px;
    padding: 15px 0;
    border-bottom: 1px solid #e2e8f0;
    font-size: 14px;
    color: #666;
  }

  .meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .meta-item i {
    color: #3498db;
    font-size: 14px;
  }

  .blog-description {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    border-left: 4px solid #3498db;
  }

  .blog-description h3 {
    color: #2c3e50;
    font-size: 16px;
    margin-bottom: 12px;
    font-weight: 600;
  }

  .blog-description p {
    color: #555;
    line-height: 1.6;
    margin: 0;
    font-size: 15px;
  }
  
  .blog-content-text {
    background: white;
    padding: 0;
    line-height: 1.7;
    color: #333;
    font-size: 16px;
  }

  .blog-content-text img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 20px 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }

  .blog-content-text h1,
  .blog-content-text h2,
  .blog-content-text h3,
  .blog-content-text h4 {
    color: #2c3e50;
    margin-top: 25px;
    margin-bottom: 15px;
    font-weight: 600;
  }

  .blog-content-text h2 {
    font-size: 22px;
    color: #34495e;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
    margin-bottom: 20px;
  }

  .blog-content-text p {
    margin-bottom: 16px;
    text-align: justify;
    line-height: 1.7;
  }

  .blog-content-text ul,
  .blog-content-text ol {
    margin: 15px 0;
    padding-left: 25px;
  }

  .blog-content-text li {
    margin-bottom: 8px;
    line-height: 1.6;
  }

  .blog-content-text blockquote {
    border-left: 4px solid #3498db;
    padding-left: 20px;
    margin: 20px 0;
    font-style: italic;
    color: #555;
    background: #f8f9fa;
    padding: 15px 20px;
    border-radius: 0 8px 8px 0;
  }
  
  .sidebar-box {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 25px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
  }

  .sidebar-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  }

  .sidebar-title {
    font-size: 20px;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 3px solid #667eea;
    position: relative;
    display: flex;
    align-items: center;
  }

  .sidebar-title::before {
    content: "";
    width: 6px;
    height: 6px;
    background: #667eea;
    border-radius: 50%;
    margin-right: 10px;
  }
  
  .category-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 18px;
    margin-bottom: 10px;
    background: white;
    color: #4a5568;
    text-decoration: none;
    border-radius: 10px;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
    font-weight: 500;
    position: relative;
    overflow: hidden;
  }

  .category-item::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
    z-index: 1;
  }

  .category-item:hover::before {
    width: 100%;
  }

  .category-item:hover {
    color: white;
    text-decoration: none;
    transform: translateX(8px);
    border-color: #667eea;
  }

  .category-item span,
  .category-item i {
    position: relative;
    z-index: 2;
  }

  .category-item i {
    font-size: 14px;
    opacity: 0.7;
    transition: all 0.3s ease;
  }

  .category-item:hover i {
    opacity: 1;
    transform: translateX(3px);
  }
  
  .popular-item {
    display: flex;
    gap: 12px;
    padding: 15px 0;
    border-bottom: 1px solid #e9ecef;
  }
  
  .popular-item:last-child {
    border-bottom: none;
  }
  
  .popular-image {
    width: 70px;
    height: 50px;
    border-radius: 6px;
    overflow: hidden;
  }
  
  .popular-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .popular-content h5 {
    font-size: 14px;
    margin: 0 0 8px 0;
    line-height: 1.4;
  }
  
  .popular-content h5 a {
    color: #2c3e50;
    text-decoration: none;
  }
  
  .popular-content h5 a:hover {
    color: #007bff;
  }
  
  .popular-meta {
    font-size: 12px;
    color: #6c757d;
  }
  
  .latest-section {
    background: #f8f9fa;
    padding: 40px;
    border-radius: 10px;
  }
  
  .latest-title {
    text-align: center;
    font-size: 24px;
    color: #2c3e50;
    margin-bottom: 30px;
    font-weight: bold;
  }
  
  .latest-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }
  
  .latest-card {
    flex: 0 0 calc(33.333% - 14px);
    background: white;
    border-radius: 10px;
    overflow: hidden;
    transition: transform 0.3s ease;
  }
  
  .latest-card:hover {
    transform: translateY(-5px);
  }
  
  .latest-image {
    width: 100%;
    height: 180px;
    overflow: hidden;
  }
  
  .latest-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .latest-content {
    padding: 20px;
  }
  
  .latest-content h4 {
    font-size: 16px;
    margin: 0 0 10px 0;
    color: #2c3e50;
  }
  
  .latest-content h4 a {
    color: inherit;
    text-decoration: none;
  }
  
  .latest-content h4 a:hover {
    color: #007bff;
  }
  
  .latest-excerpt {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 15px;
    line-height: 1.5;
  }
  
  .latest-meta {
    font-size: 12px;
    color: #6c757d;
  }
  
  @media (max-width: 768px) {
    .blog-main {
      flex-direction: column;
    }
    
    .blog-wrapper {
      padding: 20px 15px;
    }
    
    .blog-content, .blog-sidebar {
      padding: 20px;
    }
    
    .blog-title {
      font-size: 22px;
    }
    
    .latest-card {
      flex: 0 0 100%;
    }
  }
</style>

<div class="blog-wrapper">
  <?php if($dataBlog): ?>
  <!-- Main Content -->
  <div class="blog-main">
    <!-- Left Content -->
    <div class="blog-content">
      <h1 class="blog-title"><?php echo e($dataBlog->name_blog); ?></h1>

      <?php if($dataBlog->image_blog): ?>
      <div class="blog-image-container">
        <img src="<?php echo e($dataBlog->image_blog); ?>" alt="<?php echo e($dataBlog->name_blog); ?>" class="blog-main-image">
      </div>
      <?php endif; ?>

      <div class="blog-meta">
        <div class="meta-item">
          <i class="fas fa-user"></i>
          <span><?php echo e($dataBlog->author_name); ?></span>
        </div>
        <div class="meta-item">
          <i class="fas fa-calendar"></i>
          <span><?php echo e(date('d/m/Y', strtotime($dataBlog->date_blog))); ?></span>
        </div>
        <div class="meta-item">
          <i class="fas fa-eye"></i>
          <span><?php echo e(number_format($dataBlog->view_blog)); ?> lượt xem</span>
        </div>
      </div>

      <?php if($dataBlog->description_blog): ?>
      <div class="blog-description">
        <h3>Thông tin tuyển sinh năm 2025</h3>
        <p><?php echo e($dataBlog->description_blog); ?></p>
      </div>
      <?php endif; ?>

      <div class="blog-content-text">
        <?php echo $dataBlog->content_blog; ?>

      </div>
    </div>
    
    <!-- Right Sidebar -->
    <div class="blog-sidebar">
      <!-- Categories -->
      <div class="sidebar-box">
        <h3 class="sidebar-title">📂 Danh mục bài viết</h3>
        <?php $__currentLoopData = $dataBlogCategory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <a href="<?php echo e(route('user.blogList')); ?>?category=<?php echo e($category->id); ?>" class="category-item">
          <span><?php echo e($category->name_category_blog); ?></span>
          <i>→</i>
        </a>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <a href="<?php echo e(route('user.blogList')); ?>" class="category-item" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-color: #667eea;">
          <span>📋 Xem tất cả bài viết</span>
          <i>→</i>
        </a>
      </div>
      
      <!-- Popular Posts -->
      <div class="sidebar-box">
        <h3 class="sidebar-title">🔥 Bài viết phổ biến</h3>
        <?php $__currentLoopData = $popularBlogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="popular-item">
          <div class="popular-image">
            <img src="<?php echo e($blog->image_blog); ?>" alt="<?php echo e($blog->name_blog); ?>">
          </div>
          <div class="popular-content">
            <h5><a href="<?php echo e(route('user.blogDetail', $blog->id)); ?>"><?php echo e(Str::limit($blog->name_blog, 50)); ?></a></h5>
            <div class="popular-meta">
              👁️ <?php echo e(number_format($blog->view_blog)); ?> | 📅 <?php echo e(date('d/m/Y', strtotime($blog->date_blog))); ?>

            </div>
          </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
      </div>
    </div>
  </div>
  <?php else: ?>
  <div style="background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; text-align: center;">
    <strong>❌ Không tìm thấy bài viết!</strong>
  </div>
  <?php endif; ?>
  
  <!-- Latest Posts -->
  <div class="latest-section">
    <h2 class="latest-title">📰 Các bài viết mới nhất</h2>
    <div class="latest-grid">
      <?php $__currentLoopData = $latestBlogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
      <div class="latest-card">
        <div class="latest-image">
          <img src="<?php echo e($blog->image_blog); ?>" alt="<?php echo e($blog->name_blog); ?>">
        </div>
        <div class="latest-content">
          <h4><a href="<?php echo e(route('user.blogDetail', $blog->id)); ?>"><?php echo e($blog->name_blog); ?></a></h4>
          <p class="latest-excerpt"><?php echo e(Str::limit($blog->description_blog, 80)); ?></p>
          <div class="latest-meta">
            👤 <?php echo e($blog->author_name); ?> | 📅 <?php echo e(date('d/m/Y', strtotime($blog->date_blog))); ?>

          </div>
        </div>
      </div>
      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
  </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('user.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\TuyenSinhProjectV3\resources\views/user/pages/blog-detail.blade.php ENDPATH**/ ?>