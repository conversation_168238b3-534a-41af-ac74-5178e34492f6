
<?php $__env->startSection('title', $dataBlog->name_blog); ?>
<?php $__env->startSection('content'); ?>



<style>
  .blog-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 30px 20px;
    background: white;
  }
  
  .blog-main {
    display: flex;
    gap: 30px;
    margin-bottom: 50px;
    width: 100%;
  }

  .blog-content {
    flex: 0 0 70%;
    background: #f8f9fa;
    padding: 30px;
    border-radius: 10px;
  }

  .blog-sidebar {
    flex: 0 0 30%;
    background: #f8f9fa;
    padding: 30px;
    border-radius: 10px;
  }
  
  .blog-title {
    font-size: 28px;
    color: #2c3e50;
    margin-bottom: 20px;
    font-weight: bold;
    line-height: 1.3;
  }
  
  .blog-meta {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    border-left: 4px solid #007bff;
  }
  
  .meta-item {
    margin-bottom: 8px;
    color: #495057;
    font-size: 15px;
  }
  
  .meta-item strong {
    color: #007bff;
    margin-right: 8px;
  }
  
  .blog-description {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    border-left: 4px solid #28a745;
  }
  
  .blog-description h3 {
    color: #28a745;
    font-size: 18px;
    margin-bottom: 15px;
  }
  
  .blog-description p {
    color: #6c757d;
    line-height: 1.6;
    margin: 0;
  }
  
  .view-badge {
    display: inline-block;
    background: #e3f2fd;
    color: #1976d2;
    padding: 10px 20px;
    border-radius: 20px;
    font-weight: 500;
    margin-bottom: 25px;
  }
  
  .blog-content-text {
    background: white;
    padding: 25px;
    border-radius: 8px;
    line-height: 1.7;
    color: #495057;
  }
  
  .blog-content-text img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 20px 0;
  }
  
  .sidebar-box {
    background: white;
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 25px;
  }
  
  .sidebar-title {
    font-size: 18px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #007bff;
  }
  
  .category-item {
    display: block;
    padding: 12px 15px;
    margin-bottom: 8px;
    background: #f8f9fa;
    color: #495057;
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.3s ease;
  }
  
  .category-item:hover {
    background: #007bff;
    color: white;
    text-decoration: none;
    transform: translateX(5px);
  }
  
  .popular-item {
    display: flex;
    gap: 12px;
    padding: 15px 0;
    border-bottom: 1px solid #e9ecef;
  }
  
  .popular-item:last-child {
    border-bottom: none;
  }
  
  .popular-image {
    width: 70px;
    height: 50px;
    border-radius: 6px;
    overflow: hidden;
  }
  
  .popular-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .popular-content h5 {
    font-size: 14px;
    margin: 0 0 8px 0;
    line-height: 1.4;
  }
  
  .popular-content h5 a {
    color: #2c3e50;
    text-decoration: none;
  }
  
  .popular-content h5 a:hover {
    color: #007bff;
  }
  
  .popular-meta {
    font-size: 12px;
    color: #6c757d;
  }
  
  .latest-section {
    background: #f8f9fa;
    padding: 40px;
    border-radius: 10px;
  }
  
  .latest-title {
    text-align: center;
    font-size: 24px;
    color: #2c3e50;
    margin-bottom: 30px;
    font-weight: bold;
  }
  
  .latest-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }
  
  .latest-card {
    flex: 0 0 calc(33.333% - 14px);
    background: white;
    border-radius: 10px;
    overflow: hidden;
    transition: transform 0.3s ease;
  }
  
  .latest-card:hover {
    transform: translateY(-5px);
  }
  
  .latest-image {
    width: 100%;
    height: 180px;
    overflow: hidden;
  }
  
  .latest-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .latest-content {
    padding: 20px;
  }
  
  .latest-content h4 {
    font-size: 16px;
    margin: 0 0 10px 0;
    color: #2c3e50;
  }
  
  .latest-content h4 a {
    color: inherit;
    text-decoration: none;
  }
  
  .latest-content h4 a:hover {
    color: #007bff;
  }
  
  .latest-excerpt {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 15px;
    line-height: 1.5;
  }
  
  .latest-meta {
    font-size: 12px;
    color: #6c757d;
  }
  
  @media (max-width: 768px) {
    .blog-main {
      flex-direction: column;
    }
    
    .blog-wrapper {
      padding: 20px 15px;
    }
    
    .blog-content, .blog-sidebar {
      padding: 20px;
    }
    
    .blog-title {
      font-size: 22px;
    }
    
    .latest-card {
      flex: 0 0 100%;
    }
  }
</style>

<div class="blog-wrapper">
  <?php if($dataBlog): ?>
  <!-- Main Content -->
  <div class="blog-main">
    <!-- Left Content -->
    <div class="blog-content">
      <h1 class="blog-title"><?php echo e($dataBlog->name_blog); ?></h1>
      
      <div class="blog-meta">
        <div class="meta-item">
          <strong>👤 Tác giả:</strong> <?php echo e($dataBlog->author_name); ?>

        </div>
        <div class="meta-item">
          <strong>📅 Ngày đăng:</strong> <?php echo e(date('d/m/Y', strtotime($dataBlog->date_blog))); ?>

        </div>
        <div class="meta-item">
          <strong>📂 Danh mục:</strong> <?php echo e($dataBlog->category_name); ?>

        </div>
      </div>
      
      <div class="view-badge">
        👁️ <?php echo e(number_format($dataBlog->view_blog)); ?> lượt xem
      </div>
      
      <?php if($dataBlog->description_blog): ?>
      <div class="blog-description">
        <h3>📝 Tóm tắt nội dung</h3>
        <p><?php echo e($dataBlog->description_blog); ?></p>
      </div>
      <?php endif; ?>
      
      <div class="blog-content-text">
        <?php if($dataBlog->image_blog): ?>
          <img src="<?php echo e($dataBlog->image_blog); ?>" alt="<?php echo e($dataBlog->name_blog); ?>">
        <?php endif; ?>
        <?php echo $dataBlog->content_blog; ?>

      </div>
    </div>
    
    <!-- Right Sidebar -->
    <div class="blog-sidebar">
      <!-- Categories -->
      <div class="sidebar-box">
        <h3 class="sidebar-title">📂 Danh mục bài viết</h3>
        <?php $__currentLoopData = $dataBlogCategory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <a href="<?php echo e(route('user.blogList')); ?>?category=<?php echo e($category->id); ?>" class="category-item">
          <?php echo e($category->name_category_blog); ?>

        </a>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <a href="<?php echo e(route('user.blogList')); ?>" class="category-item" style="background: #007bff; color: white;">
          📋 Xem tất cả bài viết
        </a>
      </div>
      
      <!-- Popular Posts -->
      <div class="sidebar-box">
        <h3 class="sidebar-title">🔥 Bài viết phổ biến</h3>
        <?php $__currentLoopData = $popularBlogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="popular-item">
          <div class="popular-image">
            <img src="<?php echo e($blog->image_blog); ?>" alt="<?php echo e($blog->name_blog); ?>">
          </div>
          <div class="popular-content">
            <h5><a href="<?php echo e(route('user.blogDetail', $blog->id)); ?>"><?php echo e(Str::limit($blog->name_blog, 50)); ?></a></h5>
            <div class="popular-meta">
              👁️ <?php echo e(number_format($blog->view_blog)); ?> | 📅 <?php echo e(date('d/m/Y', strtotime($blog->date_blog))); ?>

            </div>
          </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
      </div>
    </div>
  </div>
  <?php else: ?>
  <div style="background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; text-align: center;">
    <strong>❌ Không tìm thấy bài viết!</strong>
  </div>
  <?php endif; ?>
  
  <!-- Latest Posts -->
  <div class="latest-section">
    <h2 class="latest-title">📰 Các bài viết mới nhất</h2>
    <div class="latest-grid">
      <?php $__currentLoopData = $latestBlogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
      <div class="latest-card">
        <div class="latest-image">
          <img src="<?php echo e($blog->image_blog); ?>" alt="<?php echo e($blog->name_blog); ?>">
        </div>
        <div class="latest-content">
          <h4><a href="<?php echo e(route('user.blogDetail', $blog->id)); ?>"><?php echo e($blog->name_blog); ?></a></h4>
          <p class="latest-excerpt"><?php echo e(Str::limit($blog->description_blog, 80)); ?></p>
          <div class="latest-meta">
            👤 <?php echo e($blog->author_name); ?> | 📅 <?php echo e(date('d/m/Y', strtotime($blog->date_blog))); ?>

          </div>
        </div>
      </div>
      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
  </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('user.layout', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\TuyenSinhProjectV3\resources\views/user/pages/blog-detail.blade.php ENDPATH**/ ?>