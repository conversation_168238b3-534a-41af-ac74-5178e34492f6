-- X<PERSON>a dữ liệu cũ trong bảng major_subject_combinations
-- v<PERSON> bây giờ cần có admission_method_id để phân biệt theo phương thức

-- 1. <PERSON><PERSON><PERSON> tra dữ liệu hiện tại
SELECT 
    msc.id,
    m.name_major,
    sc.code,
    msc.admission_method_id
FROM major_subject_combinations msc
JOIN majors m ON msc.major_id = m.id
JOIN subject_combinations sc ON msc.subject_combination_id = sc.id
ORDER BY m.name_major, sc.code;

-- 2. <PERSON><PERSON><PERSON> các record có admission_method_id = NULL (dữ liệu cũ)
DELETE FROM major_subject_combinations WHERE admission_method_id IS NULL;

-- 3. Kiểm tra lại sau khi xóa
SELECT COUNT(*) as remaining_records FROM major_subject_combinations;

-- 4. Hiển thị dữ liệu còn lại
SELECT 
    msc.id,
    m.name_major,
    am.name as admission_method,
    sc.code as combination_code
FROM major_subject_combinations msc
JOIN majors m ON msc.major_id = m.id
JOIN admission_methods am ON msc.admission_method_id = am.id
JOIN subject_combinations sc ON msc.subject_combination_id = sc.id
ORDER BY m.name_major, am.priority_order, sc.code;
