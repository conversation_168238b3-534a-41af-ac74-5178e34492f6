# Debug Hướng dẫn - Vấn đề load điểm chuẩn trong form chỉnh sửa

## 🐛 Vấn đề
Khi chỉnh sửa ngành học, phần điểm chuẩn không được load lại mà phải nhập lại từ đầu.

## 🔧 Các thay đổi đã thực hiện

### 1. Sửa thứ tự load dữ liệu
- **Trước**: Load điểm chuẩn cuối cùng
- **Sau**: Load điểm chuẩn trước, sau đó load phương thức xét tuyển và render

### 2. Thêm debug logs
- Console logs để theo dõi quá trình load dữ liệu
- Logs cho API responses và data processing

### 3. C<PERSON>i thiện timing
- Thêm setTimeout để đảm bảo dữ liệu được load đầy đủ
- Render điểm chuẩn sau khi có đầy đủ thông tin phương thức

### 4. Thêm manual reload
- But<PERSON> "Tải lại điểm chuẩn" để reload manually nếu cần
- Function `reloadAdmissionScores()` có thể gọi từ console

### 5. Cải thiện error handling
- Better fallback khi load data thất bại
- Kiểm tra đầy đủ dữ liệu trước khi render

## 🔍 Cách debug

### 1. Mở Developer Console
- F12 → Console tab
- Xem các log messages khi load trang

### 2. Kiểm tra API responses
```javascript
// Gọi từ console để test API
testAdmissionScoresAPI()
```

### 3. Kiểm tra dữ liệu loaded
```javascript
// Kiểm tra dữ liệu trong memory
console.log('Admission Methods:', admissionMethods);
console.log('Selected Methods:', selectedAdmissionMethods);
console.log('Admission Scores:', admissionScores);
```

### 4. Manual reload
- Click button "Tải lại điểm chuẩn"
- Hoặc gọi từ console: `reloadAdmissionScores()`

## 📊 Dữ liệu test
- Ngành học ID 14 có dữ liệu điểm chuẩn từ 2021-2025
- API endpoint: `/api/major/14/admission-scores`

## ⚠️ Các vấn đề có thể gặp

### 1. Timing issues
- **Triệu chứng**: Điểm chuẩn không hiển thị
- **Giải pháp**: Sử dụng button "Tải lại điểm chuẩn"

### 2. Data type mismatch
- **Triệu chứng**: Không tìm thấy điểm chuẩn cho phương thức
- **Giải pháp**: Sử dụng `==` thay vì `===` để so sánh ID

### 3. Missing admission methods
- **Triệu chứng**: Không hiển thị table điểm chuẩn
- **Giải pháp**: Đảm bảo có ít nhất 1 phương thức được chọn

## 🚀 Test cases

### Test case 1: Ngành có điểm chuẩn
1. Vào edit ngành học ID 14
2. Kiểm tra console logs
3. Xác nhận điểm chuẩn hiển thị đúng

### Test case 2: Ngành không có điểm chuẩn
1. Vào edit ngành học mới
2. Kiểm tra table vẫn hiển thị với input trống
3. Nhập điểm chuẩn và save

### Test case 3: Manual reload
1. Vào edit bất kỳ ngành học nào
2. Click "Tải lại điểm chuẩn"
3. Xác nhận dữ liệu được refresh

## 📝 Logs cần chú ý
- `Loaded admission scores:` - Dữ liệu điểm chuẩn từ API
- `Selected admission methods:` - Phương thức đã chọn
- `Score for method X, year Y:` - Điểm chuẩn cụ thể
- `Rendered admission scores table` - Xác nhận table đã render

## 🔄 Workflow mới
1. Load subject combinations
2. Load current subject combinations  
3. **Load admission scores** (mới)
4. Load admission methods
5. Load current admission methods
6. Render admission methods
7. **Render admission scores** (với delay)

Workflow này đảm bảo điểm chuẩn được load và hiển thị đúng cách.
