# Hướng dẫn sử dụng chức năng Tổ hợp môn riêng biệt cho từng phương thức xét tuyển

## 🎯 Tổng quan
Hệ thống đã được cập nhật để cho phép chọn **tổ hợp môn xét tuyển riêng biệt** cho từng phương thức xét tuyển. Mỗi phương thức (THPT, Học bạ) có thể có tổ hợp môn khác nhau.

## 🔧 Thay đổi kỹ thuật

### 1. Database
- **Bảng `admission_methods`**:
  - Ph<PERSON>ơng thức "Học bạ" (code: HOCBA) đã được đặt `requires_subject_combinations = 1`
  - Cho phép chọn tổ hợp môn tương tự như phương thức THPT
- **Bảng `major_subject_combinations`**:
  - Thêm trường `admission_method_id` để phân biệt tổ hợp môn theo từng phương thức
  - Cập nhật unique constraint: `(major_id, subject_combination_id, admission_method_id)`

### 2. Backend (Controller)
- **MajorAdminController.php**:
  - Cập nhật logic lưu tổ hợp môn với `admission_method_id`
  - Hỗ trợ lưu tổ hợp môn riêng biệt cho từng phương thức
- **SubjectCombinationController.php**:
  - Thêm API `getMajorSubjectCombinationsByMethod()` để lấy tổ hợp môn theo phương thức
  - Cập nhật API hiện tại để trả về thông tin phương thức
- **HomeUserController.php**:
  - Cập nhật logic hiển thị tổ hợp môn theo từng phương thức trên trang user

### 3. Frontend (Admin Interface)
- **Form thêm/sửa ngành học**:
  - Hiển thị tổ hợp môn riêng biệt cho từng phương thức
  - Input name: `subject_combinations[methodId][]`
- **JavaScript**:
  - Cập nhật logic để xử lý tổ hợp môn theo từng phương thức
  - Load và hiển thị tổ hợp môn đã chọn cho từng phương thức riêng biệt

## 📋 Hướng dẫn sử dụng

### Thêm ngành học mới:
1. Vào **Admin Panel** → **Quản lý ngành học** → **Thêm ngành học**
2. Điền thông tin cơ bản của ngành học
3. Trong phần **"Phương thức xét tuyển"**:
   - ✅ Chọn các phương thức mong muốn (THPT, Học bạ, v.v.)
   - ✅ Với mỗi phương thức được chọn, phần **"Tổ hợp môn xét tuyển"** sẽ hiển thị riêng biệt
   - ✅ Chọn tổ hợp môn phù hợp cho từng phương thức:
     - **THPT**: A00, A01, B00, v.v.
     - **Học bạ**: C01, D01, D07, v.v. (có thể khác với THPT)
4. Điền điểm chuẩn 5 năm nếu có
5. Lưu ngành học

### Cập nhật ngành học:
1. Vào **Admin Panel** → **Quản lý ngành học** → **Sửa ngành học**
2. Trong phần **"Phương thức xét tuyển"**:
   - ✅ Chọn/bỏ chọn các phương thức xét tuyển
   - ✅ Với mỗi phương thức, chọn/bỏ chọn tổ hợp môn riêng biệt
   - ✅ Tổ hợp môn đã chọn trước đó sẽ được hiển thị đúng cho từng phương thức
3. Cập nhật thông tin khác nếu cần
4. Lưu thay đổi

## 🎨 Hiển thị trên giao diện người dùng
- Trang chi tiết ngành học sẽ hiển thị:
  - ✅ **Từng phương thức xét tuyển riêng biệt** với tổ hợp môn tương ứng
  - ✅ **THPT**: Hiển thị tổ hợp môn đã chọn cho THPT (ví dụ: A00, A01)
  - ✅ **Học bạ**: Hiển thị tổ hợp môn đã chọn cho Học bạ (ví dụ: C01, D01)
  - ✅ Điểm chuẩn năm hiện tại cho từng phương thức
  - ✅ Button "Biểu đồ so sánh điểm chuẩn" cho từng phương thức

## 🔍 Kiểm tra chức năng
1. **Test case 1**: Ngành có cả THPT và Học bạ
   - Thêm ngành học với cả 2 phương thức
   - THPT chọn: A00, A01
   - Học bạ chọn: C01, D01
   - Kiểm tra hiển thị riêng biệt trên trang chi tiết

2. **Test case 2**: Ngành chỉ có một phương thức
   - Thêm ngành học chỉ với phương thức Học bạ
   - Chọn tổ hợp môn cho Học bạ
   - Kiểm tra không hiển thị tổ hợp môn cho THPT

## 📊 Lợi ích
- **Chính xác hơn**: Mỗi phương thức có tổ hợp môn riêng, phù hợp với thực tế
- **Linh hoạt hơn**: Admin có thể cấu hình tổ hợp môn khác nhau cho từng phương thức
- **Rõ ràng hơn**: Người dùng thấy chính xác tổ hợp môn cho từng phương thức
- **Mở rộng tốt**: Dễ dàng thêm phương thức mới có yêu cầu tổ hợp môn

## ⚠️ Lưu ý quan trọng
- **Dữ liệu cũ**: Các tổ hợp môn cũ (không có admission_method_id) đã được xóa
- **Cần thiết lập lại**: Admin cần chọn lại tổ hợp môn cho các ngành học hiện có
- **Phương thức hỗ trợ**: Hiện tại THPT và Học bạ đều hỗ trợ tổ hợp môn
- **Unique constraint**: Một ngành không thể có cùng tổ hợp môn cho cùng một phương thức

## 🚀 Các bước triển khai đã hoàn thành
1. ✅ Cập nhật database schema
2. ✅ Cập nhật backend controllers
3. ✅ Cập nhật frontend admin forms
4. ✅ Cập nhật giao diện người dùng
5. ✅ Xóa dữ liệu cũ không hợp lệ
6. ✅ Thêm API mới cho tổ hợp môn theo phương thức
